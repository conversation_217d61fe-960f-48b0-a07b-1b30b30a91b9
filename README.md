# MRS Reader

一个用于读取 Mihomo/Clash.Meta .mrs 规则文件的 Go 程序。

## 功能特性

- 支持读取 Zstandard 压缩的二进制 .mrs 文件
- 支持文本格式的 .mrs 文件（作为后备选项）
- 多种输出格式：默认格式、JSON 格式、纯文本格式
- 自动检测文件格式并选择合适的解析策略

## 安装

确保你已经安装了 Go 1.16 或更高版本。

```bash
# 克隆或下载源代码
# 编译程序
go build -o mrs-reader main.go
```

## 使用方法

### 基本用法

```bash
# 读取 .mrs 文件并显示规则
./mrs-reader proxy.mrs

# 显示帮助信息
./mrs-reader --help
```

### 输出格式选项

```bash
# 默认格式输出
./mrs-reader proxy.mrs

# JSON 格式输出
./mrs-reader proxy.mrs --json

# 纯文本格式输出（仅显示规则字符串）
./mrs-reader proxy.mrs --plain
```

## 输出示例

### 默认格式
```
MRS File: proxy.mrs
Version: 1
Total Rules: 0

Note: This .mrs file uses a binary format that requires specialized parsing.
The file was successfully decompressed using Zstandard, but the binary rule
format is not yet fully supported by this parser.

File information:
- Magic: MRS 
- Successfully decompressed: Yes
- Format: Binary MRS (Mihomo Rule Set)
```

### JSON 格式
```json
{
  "version": 1,
  "total_rules": 11,
  "rules": [
    {
      "index": 1,
      "type": 0,
      "content": "DOMAIN,google.com"
    },
    {
      "index": 2,
      "type": 0,
      "content": "DOMAIN,youtube.com"
    }
  ]
}
```

### 纯文本格式
```
DOMAIN,google.com
DOMAIN,youtube.com
DOMAIN,facebook.com
DOMAIN-SUFFIX,github.com
```

## 支持的文件格式

1. **二进制 MRS 文件**：Zstandard 压缩的 Mihomo 规则集文件
2. **文本 MRS 文件**：包含规则字符串的纯文本文件（每行一个规则）

## 注意事项

- 目前对二进制 .mrs 文件格式的支持还在开发中
- 程序能够成功解压缩 Zstandard 压缩的文件，但完整的二进制格式解析仍需要进一步开发
- 文本格式的 .mrs 文件可以完全支持

## 依赖项

- `github.com/klauspost/compress/zstd` - 用于 Zstandard 解压缩

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个工具。
