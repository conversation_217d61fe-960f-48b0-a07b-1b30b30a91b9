package main

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"os"
	"strings"
)

// MRSHeader represents the header of an MRS file
type MRSHeader struct {
	Magic   [4]byte // Magic number to identify MRS files
	Version uint32  // Version of the MRS format
	Count   uint32  // Number of rules in the file
}

// Rule represents a single rule in the MRS file
type Rule struct {
	Type    uint8  // Rule type (domain, IP, etc.)
	Length  uint32 // Length of the rule string
	Content string // The actual rule content
}

// MRSFile represents the complete MRS file structure
type MRSFile struct {
	Header MRSHeader
	Rules  []Rule
}

func main() {
	if len(os.Args) < 2 || len(os.Args) > 3 {
		printUsage()
		os.Exit(1)
	}

	filename := os.Args[1]
	outputFormat := "default"

	// Parse command line arguments
	if len(os.Args) == 3 {
		switch os.Args[2] {
		case "--json", "-j":
			outputFormat = "json"
		case "--plain", "-p":
			outputFormat = "plain"
		case "--help", "-h":
			printUsage()
			os.Exit(0)
		default:
			fmt.Fprintf(os.Stderr, "Error: Unknown option %s\n", os.Args[2])
			printUsage()
			os.Exit(1)
		}
	}

	// Check if file exists and has .mrs extension
	if !strings.HasSuffix(strings.ToLower(filename), ".mrs") {
		fmt.Fprintf(os.Stderr, "Error: File must have .mrs extension\n")
		os.Exit(1)
	}

	mrsFile, err := parseMRSFile(filename)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error parsing MRS file: %v\n", err)
		os.Exit(1)
	}

	// Output the rules in the specified format
	switch outputFormat {
	case "json":
		outputJSON(mrsFile)
	case "plain":
		outputPlain(mrsFile)
	default:
		outputDefault(filename, mrsFile)
	}
}

func printUsage() {
	fmt.Fprintf(os.Stderr, "Usage: %s <mrs_file> [options]\n", os.Args[0])
	fmt.Fprintf(os.Stderr, "Options:\n")
	fmt.Fprintf(os.Stderr, "  --json, -j    Output in JSON format\n")
	fmt.Fprintf(os.Stderr, "  --plain, -p   Output only rule strings (one per line)\n")
	fmt.Fprintf(os.Stderr, "  --help, -h    Show this help message\n")
	fmt.Fprintf(os.Stderr, "\nExample: %s rules.mrs\n", os.Args[0])
	fmt.Fprintf(os.Stderr, "Example: %s rules.mrs --json\n", os.Args[0])
}

// parseMRSFile parses an MRS file and returns the parsed structure
func parseMRSFile(filename string) (*MRSFile, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Try different parsing strategies
	// Strategy 1: Try to parse as binary format
	mrsFile, err := parseBinaryMRS(file)
	if err == nil {
		return mrsFile, nil
	}

	// Strategy 2: Try to parse as text format (fallback)
	file.Seek(0, 0) // Reset file pointer
	return parseTextMRS(file)
}

// parseBinaryMRS attempts to parse MRS file as binary format
func parseBinaryMRS(file *os.File) (*MRSFile, error) {
	var mrsFile MRSFile

	// Read header
	err := binary.Read(file, binary.LittleEndian, &mrsFile.Header)
	if err != nil {
		return nil, fmt.Errorf("failed to read header: %w", err)
	}

	// Validate magic number (common patterns for binary formats)
	magicStr := string(mrsFile.Header.Magic[:])
	if !isValidMagic(magicStr) {
		return nil, fmt.Errorf("invalid magic number: %s", magicStr)
	}

	// Read rules
	mrsFile.Rules = make([]Rule, mrsFile.Header.Count)
	for i := uint32(0); i < mrsFile.Header.Count; i++ {
		rule, err := readBinaryRule(file)
		if err != nil {
			return nil, fmt.Errorf("failed to read rule %d: %w", i, err)
		}
		mrsFile.Rules[i] = *rule
	}

	return &mrsFile, nil
}

// isValidMagic checks if the magic number is valid for MRS files
func isValidMagic(magic string) bool {
	// Common magic numbers for binary formats
	validMagics := []string{
		"MRS\x00", "MRS1", "MRST", "\x00MRS",
		"RULE", "CLSH", "META", "MIHO",
	}

	for _, valid := range validMagics {
		if magic == valid {
			return true
		}
	}

	// Also accept if it looks like printable text (might be a different format)
	for _, b := range []byte(magic) {
		if b != 0 && (b < 32 || b > 126) {
			return false
		}
	}
	return true
}

// readBinaryRule reads a single rule from binary format
func readBinaryRule(file *os.File) (*Rule, error) {
	var rule Rule

	// Read rule type
	err := binary.Read(file, binary.LittleEndian, &rule.Type)
	if err != nil {
		return nil, err
	}

	// Read content length
	err = binary.Read(file, binary.LittleEndian, &rule.Length)
	if err != nil {
		return nil, err
	}

	// Read content
	if rule.Length > 0 {
		content := make([]byte, rule.Length)
		_, err = file.Read(content)
		if err != nil {
			return nil, err
		}
		rule.Content = string(content)
	}

	return &rule, nil
}

// parseTextMRS attempts to parse MRS file as text format (fallback)
func parseTextMRS(file *os.File) (*MRSFile, error) {
	var mrsFile MRSFile
	var rules []Rule

	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, "//") {
			continue
		}

		// Create rule from text line
		rule := Rule{
			Type:    0, // Default type for text rules
			Length:  uint32(len(line)),
			Content: line,
		}

		rules = append(rules, rule)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	// Set header information
	mrsFile.Header.Magic = [4]byte{'T', 'X', 'T', '\x00'}
	mrsFile.Header.Version = 1
	mrsFile.Header.Count = uint32(len(rules))
	mrsFile.Rules = rules

	return &mrsFile, nil
}

// outputDefault outputs the MRS file information in default format
func outputDefault(filename string, mrsFile *MRSFile) {
	fmt.Printf("MRS File: %s\n", filename)
	fmt.Printf("Version: %d\n", mrsFile.Header.Version)
	fmt.Printf("Total Rules: %d\n", mrsFile.Header.Count)
	fmt.Println("Rules:")
	fmt.Println("------")

	for i, rule := range mrsFile.Rules {
		fmt.Printf("%d: %s\n", i+1, rule.Content)
	}
}

// outputPlain outputs only the rule strings, one per line
func outputPlain(mrsFile *MRSFile) {
	for _, rule := range mrsFile.Rules {
		fmt.Println(rule.Content)
	}
}

// outputJSON outputs the MRS file information in JSON format
func outputJSON(mrsFile *MRSFile) {
	fmt.Printf("{\n")
	fmt.Printf("  \"version\": %d,\n", mrsFile.Header.Version)
	fmt.Printf("  \"total_rules\": %d,\n", mrsFile.Header.Count)
	fmt.Printf("  \"rules\": [\n")

	for i, rule := range mrsFile.Rules {
		fmt.Printf("    {\n")
		fmt.Printf("      \"index\": %d,\n", i+1)
		fmt.Printf("      \"type\": %d,\n", rule.Type)
		fmt.Printf("      \"content\": %q\n", rule.Content)
		if i < len(mrsFile.Rules)-1 {
			fmt.Printf("    },\n")
		} else {
			fmt.Printf("    }\n")
		}
	}

	fmt.Printf("  ]\n")
	fmt.Printf("}\n")
}
