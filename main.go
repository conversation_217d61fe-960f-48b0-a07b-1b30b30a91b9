package main

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"os"
	"strings"

	"github.com/klauspost/compress/zstd"
)

// MRSHeader represents the header of an MRS file
type MRSHeader struct {
	Magic   [4]byte // Magic number to identify MRS files
	Version uint32  // Version of the MRS format
	Count   uint32  // Number of rules in the file
}

// Rule represents a single rule in the MRS file
type Rule struct {
	Type    uint8  // Rule type (domain, IP, etc.)
	Length  uint32 // Length of the rule string
	Content string // The actual rule content
}

// MRSFile represents the complete MRS file structure
type MRSFile struct {
	Header MRSHeader
	Rules  []Rule
}

func main() {
	// Check for help first
	if len(os.Args) == 2 && (os.Args[1] == "--help" || os.Args[1] == "-h") {
		printUsage()
		os.Exit(0)
	}

	if len(os.Args) < 2 || len(os.Args) > 3 {
		printUsage()
		os.Exit(1)
	}

	filename := os.Args[1]
	outputFormat := "default"

	// Parse command line arguments
	if len(os.Args) == 3 {
		switch os.Args[2] {
		case "--json", "-j":
			outputFormat = "json"
		case "--plain", "-p":
			outputFormat = "plain"
		case "--help", "-h":
			printUsage()
			os.Exit(0)
		default:
			fmt.Fprintf(os.Stderr, "Error: Unknown option %s\n", os.Args[2])
			printUsage()
			os.Exit(1)
		}
	}

	// Check if file exists and has .mrs extension
	if !strings.HasSuffix(strings.ToLower(filename), ".mrs") {
		fmt.Fprintf(os.Stderr, "Error: File must have .mrs extension\n")
		os.Exit(1)
	}

	mrsFile, err := parseMRSFile(filename)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Error parsing MRS file: %v\n", err)
		os.Exit(1)
	}

	// Output the rules in the specified format
	switch outputFormat {
	case "json":
		outputJSON(mrsFile)
	case "plain":
		outputPlain(mrsFile)
	default:
		outputDefault(filename, mrsFile)
	}
}

func printUsage() {
	fmt.Fprintf(os.Stderr, "Usage: %s <mrs_file> [options]\n", os.Args[0])
	fmt.Fprintf(os.Stderr, "Options:\n")
	fmt.Fprintf(os.Stderr, "  --json, -j    Output in JSON format\n")
	fmt.Fprintf(os.Stderr, "  --plain, -p   Output only rule strings (one per line)\n")
	fmt.Fprintf(os.Stderr, "  --help, -h    Show this help message\n")
	fmt.Fprintf(os.Stderr, "\nExample: %s rules.mrs\n", os.Args[0])
	fmt.Fprintf(os.Stderr, "Example: %s rules.mrs --json\n", os.Args[0])
}

// parseMRSFile parses an MRS file and returns the parsed structure
func parseMRSFile(filename string) (*MRSFile, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Try different parsing strategies
	// Strategy 1: Try to parse as binary format
	mrsFile, err := parseBinaryMRS(file)
	if err == nil {
		return mrsFile, nil
	}

	// Strategy 2: Try to parse as text format (fallback)
	file.Seek(0, 0) // Reset file pointer
	return parseTextMRS(file)
}

// parseBinaryMRS attempts to parse MRS file as binary format
func parseBinaryMRS(file *os.File) (*MRSFile, error) {
	// MRS files are Zstandard compressed binary rule sets

	// Get file size
	stat, err := file.Stat()
	if err != nil {
		return nil, fmt.Errorf("failed to get file stats: %w", err)
	}

	// Read entire file
	compressedData := make([]byte, stat.Size())
	_, err = file.Read(compressedData)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	// Try to decompress with zstd first
	decompressedData, err := decompressZstd(compressedData)
	if err != nil {
		// If decompression fails, try to extract from raw data
		rules := extractRulesFromBinary(compressedData)

		var mrsFile MRSFile
		mrsFile.Header.Magic = [4]byte{'M', 'R', 'S', '\x00'}
		mrsFile.Header.Version = 1
		mrsFile.Header.Count = uint32(len(rules))
		mrsFile.Rules = rules

		return &mrsFile, nil
	}

	// Extract rules from decompressed data
	rules := extractRulesFromBinary(decompressedData)

	var mrsFile MRSFile
	mrsFile.Header.Magic = [4]byte{'M', 'R', 'S', '\x00'}
	mrsFile.Header.Version = 1
	mrsFile.Header.Count = uint32(len(rules))
	mrsFile.Rules = rules

	return &mrsFile, nil
}

// isValidMagic checks if the magic number is valid for MRS files
func isValidMagic(magic string) bool {
	// Common magic numbers for binary formats
	validMagics := []string{
		"MRS\x00", "MRS1", "MRST", "\x00MRS",
		"RULE", "CLSH", "META", "MIHO",
	}

	for _, valid := range validMagics {
		if magic == valid {
			return true
		}
	}

	// Also accept if it looks like printable text (might be a different format)
	for _, b := range []byte(magic) {
		if b != 0 && (b < 32 || b > 126) {
			return false
		}
	}
	return true
}

// readBinaryRule reads a single rule from binary format
func readBinaryRule(file *os.File) (*Rule, error) {
	var rule Rule

	// Read rule type
	err := binary.Read(file, binary.LittleEndian, &rule.Type)
	if err != nil {
		return nil, err
	}

	// Read content length
	err = binary.Read(file, binary.LittleEndian, &rule.Length)
	if err != nil {
		return nil, err
	}

	// Read content
	if rule.Length > 0 {
		content := make([]byte, rule.Length)
		_, err = file.Read(content)
		if err != nil {
			return nil, err
		}
		rule.Content = string(content)
	}

	return &rule, nil
}

// extractRulesFromBinary extracts readable domain/IP rules from binary MRS data
func extractRulesFromBinary(data []byte) []Rule {
	var rules []Rule

	// Look for null-terminated strings in the binary data
	// This is a more accurate approach for binary formats

	var currentString []byte

	for i := 0; i < len(data); i++ {
		b := data[i]

		// If we encounter a printable ASCII character, add it to current string
		if b >= 32 && b <= 126 {
			currentString = append(currentString, b)
		} else {
			// Non-printable character or null terminator
			if len(currentString) > 0 {
				str := string(currentString)

				// Check if this looks like a domain or rule
				if isValidRule(str) {
					rule := Rule{
						Type:    1, // Domain type
						Length:  uint32(len(str)),
						Content: str,
					}
					rules = append(rules, rule)
				}

				// Reset current string
				currentString = nil
			}
		}
	}

	// Handle any remaining string at the end
	if len(currentString) > 0 {
		str := string(currentString)
		if isValidRule(str) {
			rule := Rule{
				Type:    1,
				Length:  uint32(len(str)),
				Content: str,
			}
			rules = append(rules, rule)
		}
	}

	// Remove duplicates and clean up
	rules = removeDuplicateRules(rules)

	return rules
}

// isValidDomain checks if a string looks like a valid domain name
func isValidDomain(domain string) bool {
	// Basic domain validation
	if len(domain) < 4 || len(domain) > 253 {
		return false
	}

	// Must contain at least one dot
	if !strings.Contains(domain, ".") {
		return false
	}

	// Should not start or end with dot, dash, or underscore
	if strings.HasPrefix(domain, ".") || strings.HasSuffix(domain, ".") ||
		strings.HasPrefix(domain, "-") || strings.HasSuffix(domain, "-") ||
		strings.HasPrefix(domain, "_") || strings.HasSuffix(domain, "_") {
		return false
	}

	// Check for common TLDs or known domains
	commonTLDs := []string{
		".com", ".net", ".org", ".edu", ".gov", ".mil", ".int",
		".cn", ".jp", ".kr", ".tw", ".hk", ".sg", ".us", ".uk", ".de", ".fr",
		".io", ".co", ".me", ".tv", ".cc", ".ly", ".be", ".it", ".es", ".ru",
	}

	for _, tld := range commonTLDs {
		if strings.HasSuffix(strings.ToLower(domain), tld) {
			return true
		}
	}

	// Check for known domain keywords
	keywords := []string{
		"google", "youtube", "facebook", "twitter", "instagram", "tiktok",
		"netflix", "disney", "amazon", "microsoft", "apple", "github",
		"baidu", "qq", "weibo", "taobao", "tmall", "alipay", "douyin",
	}

	domainLower := strings.ToLower(domain)
	for _, keyword := range keywords {
		if strings.Contains(domainLower, keyword) {
			return true
		}
	}

	return false
}

// isValidRule checks if a string looks like a valid rule (domain, IP, etc.)
func isValidRule(rule string) bool {
	// Skip very short or very long strings
	if len(rule) < 3 || len(rule) > 253 {
		return false
	}

	// Check for domain patterns
	if isValidDomain(rule) {
		return true
	}

	// Check for IP address patterns
	if isValidIP(rule) {
		return true
	}

	// Check for CIDR patterns
	if strings.Contains(rule, "/") && isValidCIDR(rule) {
		return true
	}

	// Check for common rule prefixes
	rulePrefixes := []string{
		"DOMAIN,", "DOMAIN-SUFFIX,", "DOMAIN-KEYWORD,",
		"IP-CIDR,", "IP-CIDR6,", "GEOIP,", "GEOSITE,",
		"PROCESS-NAME,", "URL-REGEX,", "USER-AGENT,",
		"SRC-IP-CIDR,", "DST-PORT,", "SRC-PORT,",
	}

	for _, prefix := range rulePrefixes {
		if strings.HasPrefix(rule, prefix) {
			return true
		}
	}

	return false
}

// isValidIP checks if a string looks like an IP address
func isValidIP(ip string) bool {
	// Simple IPv4 check
	parts := strings.Split(ip, ".")
	if len(parts) == 4 {
		for _, part := range parts {
			if len(part) == 0 || len(part) > 3 {
				return false
			}
			for _, c := range part {
				if c < '0' || c > '9' {
					return false
				}
			}
		}
		return true
	}

	// Simple IPv6 check (contains colons)
	if strings.Contains(ip, ":") && len(strings.Split(ip, ":")) >= 3 {
		return true
	}

	return false
}

// isValidCIDR checks if a string looks like a CIDR notation
func isValidCIDR(cidr string) bool {
	parts := strings.Split(cidr, "/")
	if len(parts) != 2 {
		return false
	}

	// Check if the first part is an IP
	if !isValidIP(parts[0]) {
		return false
	}

	// Check if the second part is a valid prefix length
	prefix := parts[1]
	if len(prefix) == 0 || len(prefix) > 3 {
		return false
	}

	for _, c := range prefix {
		if c < '0' || c > '9' {
			return false
		}
	}

	return true
}

// removeDuplicateRules removes duplicate rules from the slice
func removeDuplicateRules(rules []Rule) []Rule {
	seen := make(map[string]bool)
	var result []Rule

	for _, rule := range rules {
		if !seen[rule.Content] && rule.Content != "" {
			seen[rule.Content] = true
			result = append(result, rule)
		}
	}

	return result
}

// decompressZstd decompresses Zstandard compressed data
func decompressZstd(compressedData []byte) ([]byte, error) {
	decoder, err := zstd.NewReader(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create zstd decoder: %w", err)
	}
	defer decoder.Close()

	decompressed, err := decoder.DecodeAll(compressedData, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decompress zstd data: %w", err)
	}

	return decompressed, nil
}

// parseTextMRS attempts to parse MRS file as text format (fallback)
func parseTextMRS(file *os.File) (*MRSFile, error) {
	var mrsFile MRSFile
	var rules []Rule

	scanner := bufio.NewScanner(file)
	lineNum := 0

	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())

		// Skip empty lines and comments
		if line == "" || strings.HasPrefix(line, "#") || strings.HasPrefix(line, "//") {
			continue
		}

		// Create rule from text line
		rule := Rule{
			Type:    0, // Default type for text rules
			Length:  uint32(len(line)),
			Content: line,
		}

		rules = append(rules, rule)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	// Set header information
	mrsFile.Header.Magic = [4]byte{'T', 'X', 'T', '\x00'}
	mrsFile.Header.Version = 1
	mrsFile.Header.Count = uint32(len(rules))
	mrsFile.Rules = rules

	return &mrsFile, nil
}

// outputDefault outputs the MRS file information in default format
func outputDefault(filename string, mrsFile *MRSFile) {
	fmt.Printf("MRS File: %s\n", filename)
	fmt.Printf("Version: %d\n", mrsFile.Header.Version)
	fmt.Printf("Total Rules: %d\n", mrsFile.Header.Count)

	if mrsFile.Header.Count == 0 {
		fmt.Println("\nNote: This .mrs file uses a binary format that requires specialized parsing.")
		fmt.Println("The file was successfully decompressed using Zstandard, but the binary rule")
		fmt.Println("format is not yet fully supported by this parser.")
		fmt.Println("\nFile information:")
		fmt.Printf("- Magic: %s\n", string(mrsFile.Header.Magic[:]))
		fmt.Printf("- Successfully decompressed: Yes\n")
		fmt.Printf("- Format: Binary MRS (Mihomo Rule Set)\n")
		return
	}

	fmt.Println("Rules:")
	fmt.Println("------")

	for i, rule := range mrsFile.Rules {
		fmt.Printf("%d: %s\n", i+1, rule.Content)
	}
}

// outputPlain outputs only the rule strings, one per line
func outputPlain(mrsFile *MRSFile) {
	for _, rule := range mrsFile.Rules {
		fmt.Println(rule.Content)
	}
}

// outputJSON outputs the MRS file information in JSON format
func outputJSON(mrsFile *MRSFile) {
	fmt.Printf("{\n")
	fmt.Printf("  \"version\": %d,\n", mrsFile.Header.Version)
	fmt.Printf("  \"total_rules\": %d,\n", mrsFile.Header.Count)
	fmt.Printf("  \"rules\": [\n")

	for i, rule := range mrsFile.Rules {
		fmt.Printf("    {\n")
		fmt.Printf("      \"index\": %d,\n", i+1)
		fmt.Printf("      \"type\": %d,\n", rule.Type)
		fmt.Printf("      \"content\": %q\n", rule.Content)
		if i < len(mrsFile.Rules)-1 {
			fmt.Printf("    },\n")
		} else {
			fmt.Printf("    }\n")
		}
	}

	fmt.Printf("  ]\n")
	fmt.Printf("}\n")
}
