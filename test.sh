#!/bin/bash

# MRS Reader 测试脚本

echo "=== MRS Reader 测试 ==="
echo

# 检查程序是否存在
if [ ! -f "./mrs-reader" ]; then
    echo "错误：mrs-reader 程序不存在，请先编译程序"
    echo "运行：go build -o mrs-reader main.go"
    exit 1
fi

echo "1. 测试帮助信息..."
./mrs-reader --help
echo

echo "2. 测试文本格式 .mrs 文件..."
./mrs-reader test.mrs
echo

echo "3. 测试 JSON 输出格式..."
./mrs-reader test.mrs --json
echo

echo "4. 测试纯文本输出格式..."
echo "规则列表："
./mrs-reader test.mrs --plain
echo

if [ -f "/Users/<USER>/Downloads/proxy.mrs" ]; then
    echo "5. 测试二进制 .mrs 文件..."
    ./mrs-reader /Users/<USER>/Downloads/proxy.mrs
    echo
else
    echo "5. 跳过二进制 .mrs 文件测试（文件不存在）"
    echo
fi

echo "6. 测试错误处理..."
echo "6.1 测试不存在的文件："
./mrs-reader nonexistent.mrs 2>&1 || true
echo

echo "6.2 测试非 .mrs 文件："
./mrs-reader main.go 2>&1 || true
echo

echo "=== 测试完成 ==="
